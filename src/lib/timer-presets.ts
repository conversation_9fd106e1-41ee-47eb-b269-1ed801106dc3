import timerPresetsData from '@/data/timer-presets.json';

export interface TimerPreset {
  id: string;
  name: string;
  duration: number; // in seconds
  minutes: number;
  description: string;
  useCases: string[];
  scenarios: string[];
}

export function getAllTimerPresets(): TimerPreset[] {
  return timerPresetsData.presets;
}

export function getTimerPresetById(id: string): TimerPreset | null {
  return timerPresetsData.presets.find(preset => preset.id === id) || null;
}

export function getTimerPresetByMinutes(minutes: number): TimerPreset | null {
  return timerPresetsData.presets.find(preset => preset.minutes === minutes) || null;
}

export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else if (minutes > 0) {
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  } else {
    return `0:${remainingSeconds.toString().padStart(2, '0')}`;
  }
}

export function formatDurationText(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (hours > 0 && minutes > 0) {
    return `${hours} hour${hours > 1 ? 's' : ''} ${minutes} minute${minutes > 1 ? 's' : ''}`;
  } else if (hours > 0) {
    return `${hours} hour${hours > 1 ? 's' : ''}`;
  } else {
    return `${minutes} minute${minutes > 1 ? 's' : ''}`;
  }
}

export function getRelatedTimerPresets(currentId: string, limit: number = 5): TimerPreset[] {
  const allPresets = getAllTimerPresets();
  const currentPreset = getTimerPresetById(currentId);
  
  if (!currentPreset) {
    return allPresets.slice(0, limit);
  }

  // Get presets with similar durations (within 30 minutes)
  const related = allPresets
    .filter(preset => preset.id !== currentId)
    .sort((a, b) => {
      const diffA = Math.abs(a.duration - currentPreset.duration);
      const diffB = Math.abs(b.duration - currentPreset.duration);
      return diffA - diffB;
    })
    .slice(0, limit);

  return related;
}

export function getPopularTimerPresets(): TimerPreset[] {
  // Return most commonly used timer durations
  const popularIds = ['5-minute', '10-minute', '15-minute', '25-minute', '30-minute'];
  return popularIds
    .map(id => getTimerPresetById(id))
    .filter((preset): preset is TimerPreset => preset !== null);
}

// Convert numbers to words for SEO variations
export function numberToWords(num: number): string {
  const numbers: Record<number, string> = {
    1: 'One',
    2: 'Two',
    3: 'Three',
    5: 'Five',
    10: 'Ten',
    15: 'Fifteen',
    20: 'Twenty',
    25: 'Twenty-Five',
    30: 'Thirty',
    45: 'Forty-Five',
    60: 'Sixty'
  };
  return numbers[num] || num.toString();
}

// Generate SEO keyword variations
export function generateTimerKeywords(minutes: number): string[] {
  const numWord = numberToWords(minutes);
  const baseKeywords = [
    `${minutes} minute timer`,
    `${minutes} min timer`,
    `${minutes} minute countdown`,
    `${numWord.toLowerCase()} minute timer`,
    `${numWord.toLowerCase()} min timer`,
    `${minutes}-minute timer`,
    `${minutes} minute online timer`,
    `${minutes} minute countdown timer`,
    `${minutes} minute timer online`,
    `${minutes} minute timer free`
  ];

  // Add specific variations based on duration
  if (minutes === 25) {
    baseKeywords.push('pomodoro timer', 'pomodoro technique timer', '25 min pomodoro');
  }
  if (minutes === 5) {
    baseKeywords.push('pomodoro break timer', 'short break timer');
  }
  if (minutes === 2) {
    baseKeywords.push('teeth brushing timer', 'dental hygiene timer');
  }
  if (minutes === 1) {
    baseKeywords.push('one minute timer', 'quick timer', 'micro break timer');
  }

  return baseKeywords;
}
