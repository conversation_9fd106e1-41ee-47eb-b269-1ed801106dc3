import Link from 'next/link';
import { MainLayout } from "@/components/layout/main-layout";
import { getAllTimerPresets } from "@/lib/timer-presets";

export default function TimerDurationNotFound() {
  const presets = getAllTimerPresets();

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="max-w-4xl mx-auto pt-32 px-4 pb-8">
          {/* Header Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
              Timer Duration Not Found
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Sorry, we don&apos;t have a preset timer for this duration yet. 
              Please choose from one of the available timer durations below, or use our custom timer.
            </p>
          </div>

          {/* Available Timer Presets */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-center mb-8 text-gray-900 dark:text-white">
              Available Timer Durations
            </h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              {presets.map((preset) => (
                <Link
                  key={preset.id}
                  href={`/timer/${preset.id}`}
                  className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600"
                >
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {preset.name}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-2">
                    {preset.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {preset.minutes} min
                    </span>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {preset.duration} seconds
                    </span>
                  </div>
                </Link>
              ))}
            </div>
          </div>

          {/* Custom Timer Option */}
          <div className="mb-12 text-center">
            <div className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 p-8 rounded-lg border border-green-200 dark:border-green-700">
              <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                Need a Custom Timer Duration?
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                Use our flexible timer to set any custom duration from seconds to hours.
              </p>
              <Link
                href="/timer"
                className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white rounded-lg font-medium transition-colors shadow-lg"
              >
                Create Custom Timer
              </Link>
            </div>
          </div>

          {/* Navigation Links */}
          <div className="text-center">
            <h3 className="text-xl font-semibold mb-6 text-gray-900 dark:text-white">
              Or explore our other tools
            </h3>
            <div className="flex flex-wrap justify-center gap-4">
              <Link
                href="/"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                Main Clock
              </Link>
              <Link
                href="/time"
                className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                World Time
              </Link>
              <Link
                href="/alarm"
                className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                Alarm Clock
              </Link>
              <Link
                href="/stopwatch"
                className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                Stopwatch
              </Link>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
