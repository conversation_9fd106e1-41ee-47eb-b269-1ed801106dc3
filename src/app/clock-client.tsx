"use client";

import { useState, useEffect } from "react";
import { DigitalClock } from "@/components/time/digital-clock";
import { ClockSettings } from "@/components/time/clock-settings";
import { useFullscreenWakeLock } from "@/hooks/useFullscreenWakeLock";
import { WakeLockIndicator } from "@/components/ui/wake-lock-indicator";

// Simple localStorage utilities
const STORAGE_KEY = "clockSettings";
//const DEFAULT_TEXT_COLOR = "#004d40";
const DEFAULT_TEXT_COLOR = "#00ff88";
//const DEFAULT_BACKGROUND_IMAGE = "linear-gradient(135deg, #e0f2f1 0%, #64ffda 100%)";
const DEFAULT_BACKGROUND_IMAGE = "linear-gradient(135deg, #48c6ef 0%, #6f35c5 30%, #1a2980 60%, #2f3f87 100%)";

interface ClockSettings {
  showSeconds: boolean;
  showWeekday: boolean;
  showDate: boolean;
  showWeekNumber: boolean;
  use12Hours: boolean;
  textColor: string;
  fontSize: string;
  fontFamily: string;
  position: { x: number; y: number };
  backgroundColor: string;
  backgroundImage: string;
}

const defaultSettings: ClockSettings = {
  showSeconds: true,
  showWeekday: true,
  showDate: true,
  showWeekNumber: true,
  use12Hours: false,
  textColor: DEFAULT_TEXT_COLOR,
  fontSize: "5rem",
  fontFamily: "monospace",
  position: { x: 0, y: 0 },
  backgroundColor: "",
  backgroundImage: "",
};

export function ClockClient() {
  const [settings, setSettings] = useState(defaultSettings);
  const [isLoaded, setIsLoaded] = useState(false);
  
  // Use the custom hook for fullscreen and wake lock management
  const { isFullScreen, isWakeLockSupported, isWakeLockActive } = useFullscreenWakeLock();

  // Destructure settings for easier access
  const {
    showSeconds,
    showWeekday,
    showDate,
    showWeekNumber,
    use12Hours,
    textColor,
    fontSize,
    fontFamily,
    position,
    backgroundColor,
    backgroundImage
  } = settings;

  // Load settings on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const parsed = JSON.parse(saved);
        setSettings({ ...defaultSettings, ...parsed });
      }
    } catch (error) {
      console.error("Error loading clock settings:", error);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // Save settings when they change (only after initial load)
  useEffect(() => {
    if (isLoaded) {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
      } catch (error) {
        console.error("Error saving clock settings:", error);
      }
    }
  }, [settings, isLoaded]);

  // Individual setter functions
  const setShowSeconds = (value: boolean) => {
    setSettings(prev => ({ ...prev, showSeconds: value }));
  };

  const setShowWeekday = (value: boolean) => {
    setSettings(prev => ({ ...prev, showWeekday: value }));
  };

  const setShowDate = (value: boolean) => {
    setSettings(prev => ({ ...prev, showDate: value }));
  };

  const setShowWeekNumber = (value: boolean) => {
    setSettings(prev => ({ ...prev, showWeekNumber: value }));
  };

  const setUse12Hours = (value: boolean) => {
    setSettings(prev => ({ ...prev, use12Hours: value }));
  };

  const setTextColor = (value: string) => {
    setSettings(prev => ({ ...prev, textColor: value }));
  };

  const setFontSize = (value: string) => {
    setSettings(prev => ({ ...prev, fontSize: value }));
  };

  const setFontFamily = (value: string) => {
    setSettings(prev => ({ ...prev, fontFamily: value }));
  };

  const setPosition = (value: { x: number; y: number }) => {
    setSettings(prev => ({ ...prev, position: value }));
  };

  const setBackgroundColor = (value: string) => {
    setSettings(prev => ({ ...prev, backgroundColor: value }));
  };

  const setBackgroundImage = (value: string) => {
    setSettings(prev => ({ ...prev, backgroundImage: value }));
  };

  // Apply background styles to HTML element for consistency with other pages
  useEffect(() => {
    if (typeof document === 'undefined') return;
    const htmlElement = document.documentElement;

    if (backgroundImage) {
      htmlElement.style.backgroundImage = `url(${backgroundImage})`;
      htmlElement.style.backgroundColor = '';
    } else if (backgroundColor) {
      htmlElement.style.backgroundImage = '';
      htmlElement.style.backgroundColor = backgroundColor;
    } else {
      // 默认渐变
      htmlElement.style.backgroundImage = DEFAULT_BACKGROUND_IMAGE;
      htmlElement.style.backgroundColor = '';
    }

    htmlElement.style.backgroundSize = 'cover';
    htmlElement.style.backgroundPosition = 'center';
    htmlElement.style.backgroundRepeat = 'no-repeat';
    htmlElement.style.backgroundAttachment = 'fixed';
  }, [backgroundColor, backgroundImage]);

  return (
    <>
      {isFullScreen ? (
        <>
          <div
            className="w-full h-screen flex items-center justify-center"
            style={{
              minHeight: '100vh', // Full viewport height
            }}
          >
            <DigitalClock
              showSeconds={showSeconds}
              showWeekday={showWeekday}
              showDate={showDate}
              showWeekNumber={showWeekNumber}
              use12Hours={use12Hours}
              textColor={textColor}
              fontSize={fontSize}
              fontFamily={fontFamily}
              position={position}
            />
          </div>

          {/* Settings panel in fullscreen mode */}
          <div className="fixed bottom-4 right-4 md:top-4 md:bottom-auto md:right-4 z-[60]">
            <ClockSettings
              showSeconds={showSeconds}
              setShowSeconds={setShowSeconds}
              showWeekday={showWeekday}
              setShowWeekday={setShowWeekday}
              showDate={showDate}
              setShowDate={setShowDate}
              showWeekNumber={showWeekNumber}
              setShowWeekNumber={setShowWeekNumber}
              use12Hours={use12Hours}
              setUse12Hours={setUse12Hours}
              textColor={textColor}
              setTextColor={setTextColor}
              fontSize={fontSize}
              setFontSize={setFontSize}
              fontFamily={fontFamily}
              setFontFamily={setFontFamily}
              position={position}
              setPosition={setPosition}
              backgroundColor={backgroundColor}
              setBackgroundColor={setBackgroundColor}
              backgroundImage={backgroundImage}
              setBackgroundImage={setBackgroundImage}
              isFullScreen={isFullScreen}
            />
          </div>

          {/* Wake Lock Status Indicator */}
          <WakeLockIndicator 
            isFullScreen={isFullScreen} 
            isSupported={isWakeLockSupported}
            isActive={isWakeLockActive}
          />
        </>
      ) : (
        <>
          <div
            className="absolute inset-0 flex items-center justify-center"
          >
            <DigitalClock
              showSeconds={showSeconds}
              showWeekday={showWeekday}
              showDate={showDate}
              showWeekNumber={showWeekNumber}
              use12Hours={use12Hours}
              textColor={textColor}
              fontSize={fontSize}
              fontFamily={fontFamily}
              position={position}
            />

            {/* Scroll indicator */}
            <div className="absolute bottom-8 left-0 right-0 flex justify-center animate-bounce">
              <div className="flex flex-col items-center text-white/80 hover:text-white transition-colors drop-shadow-lg">
                <span className="text-sm mb-2 bg-black/20 px-3 py-1 rounded-full backdrop-blur-sm">
                  Scroll for more features
                </span>
                <svg
                  className="w-6 h-6 bg-black/20 rounded-full p-1 backdrop-blur-sm"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 14l-7 7m0 0l-7-7m7 7V3"
                  />
                </svg>
              </div>
            </div>
          </div>

          {/* Settings panel - mobile: bottom-right, desktop: top-right */}
          <div className="fixed bottom-4 right-4 md:top-20 md:bottom-auto md:right-4 z-50">
            <ClockSettings
              showSeconds={showSeconds}
              setShowSeconds={setShowSeconds}
              showWeekday={showWeekday}
              setShowWeekday={setShowWeekday}
              showDate={showDate}
              setShowDate={setShowDate}
              showWeekNumber={showWeekNumber}
              setShowWeekNumber={setShowWeekNumber}
              use12Hours={use12Hours}
              setUse12Hours={setUse12Hours}
              textColor={textColor}
              setTextColor={setTextColor}
              fontSize={fontSize}
              setFontSize={setFontSize}
              fontFamily={fontFamily}
              setFontFamily={setFontFamily}
              position={position}
              setPosition={setPosition}
              backgroundColor={backgroundColor}
              setBackgroundColor={setBackgroundColor}
              backgroundImage={backgroundImage}
              setBackgroundImage={setBackgroundImage}
              isFullScreen={isFullScreen}
            />
          </div>
        </>
      )}
    </>
  );
}
