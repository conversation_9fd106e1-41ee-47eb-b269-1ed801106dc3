import Link from 'next/link';
import { Footer } from './Footer';
import { TimerPreset, getRelatedTimerPresets, getPopularTimerPresets, numberToWords, generateTimerKeywords } from '@/lib/timer-presets';

interface TimerDurationSEOContentProps {
  preset: TimerPreset;
  breadcrumbItems?: Array<{
    label: string;
    href?: string;
    icon?: React.ReactNode;
  }>;
}

export function TimerDurationSEOContent({ preset, breadcrumbItems }: TimerDurationSEOContentProps) {
  const relatedPresets = getRelatedTimerPresets(preset.id, 4);
  const popularPresets = getPopularTimerPresets();
  const minutesInWords = numberToWords(preset.minutes);
  const keywords = generateTimerKeywords(preset.minutes);

  return (
    <div className="w-full bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
      <div className="max-w-6xl mx-auto px-4 pt-20 pb-12 space-y-12">
        {/* Breadcrumb Navigation */}
        {breadcrumbItems && (
          <nav className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            {breadcrumbItems.map((item, index) => (
              <div key={index} className="flex items-center">
                {index > 0 && <span className="mx-2">/</span>}
                {item.icon && <span className="mr-1">{item.icon}</span>}
                {item.href ? (
                  <Link href={item.href} className="hover:text-blue-600 dark:hover:text-blue-400">
                    {item.label}
                  </Link>
                ) : (
                  <span className="text-gray-900 dark:text-gray-100">{item.label}</span>
                )}
              </div>
            ))}
          </nav>
        )}

        {/* Hero Section */}
        <section className="text-center space-y-6">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white">
            {preset.name} – {minutesInWords} Minute Online Timer Free
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto">
            {preset.description}. Set a precise {preset.minutes}-minute ({minutesInWords.toLowerCase()}-minute) countdown timer with custom alerts,
            fullscreen mode, and no sleep interruption. Perfect for productivity, cooking, exercise, and daily activities.
            This {preset.minutes} min timer works instantly in your browser with no downloads required.
          </p>
          <div className="text-sm text-gray-500 dark:text-gray-400 max-w-3xl mx-auto">
            <strong>Also known as:</strong> {keywords.slice(0, 6).join(', ')}
          </div>
        </section>

        {/* What is this timer for */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            What is a {preset.name} Used For?
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-4 text-blue-900 dark:text-blue-100">
                🎯 Common Use Cases
              </h3>
              <ul className="space-y-2 text-blue-800 dark:text-blue-200">
                {preset.useCases.map((useCase, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    <span>{useCase}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-4 text-green-900 dark:text-green-100">
                💡 Perfect Scenarios
              </h3>
              <ul className="space-y-2 text-green-800 dark:text-green-200">
                {preset.scenarios.map((scenario, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-green-500 mr-2">•</span>
                    <span>{scenario}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </section>

        {/* Pro Tips Section */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            Pro Tips for Using Your {minutesInWords} Minute Timer
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 p-6 rounded-lg border border-yellow-200 dark:border-yellow-700">
              <h3 className="text-lg font-semibold mb-3 text-yellow-900 dark:text-yellow-100">
                💡 Maximize Focus
              </h3>
              <ul className="text-sm text-yellow-800 dark:text-yellow-200 space-y-2">
                <li>• Use fullscreen mode to eliminate distractions</li>
                <li>• Turn off notifications on your device</li>
                <li>• Place your phone in another room</li>
                <li>• Set a clear goal before starting</li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-6 rounded-lg border border-green-200 dark:border-green-700">
              <h3 className="text-lg font-semibold mb-3 text-green-900 dark:text-green-100">
                🔊 Sound Strategy
              </h3>
              <ul className="text-sm text-green-800 dark:text-green-200 space-y-2">
                <li>• Test your alarm sound before starting</li>
                <li>• Choose a pleasant but noticeable tone</li>
                <li>• Adjust volume to your environment</li>
                <li>• Use different sounds for different activities</li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 p-6 rounded-lg border border-blue-200 dark:border-blue-700">
              <h3 className="text-lg font-semibold mb-3 text-blue-900 dark:text-blue-100">
                ⚡ Productivity Hacks
              </h3>
              <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-2">
                <li>• Prepare everything before starting the timer</li>
                <li>• Use the &quot;two-minute rule&quot; for quick tasks</li>
                <li>• Take notes of what you accomplished</li>
                <li>• Reward yourself after completing sessions</li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-6 rounded-lg border border-purple-200 dark:border-purple-700">
              <h3 className="text-lg font-semibold mb-3 text-purple-900 dark:text-purple-100">
                🎯 Goal Setting
              </h3>
              <ul className="text-sm text-purple-800 dark:text-purple-200 space-y-2">
                <li>• Set specific, measurable objectives</li>
                <li>• Break large tasks into {preset.minutes}-minute chunks</li>
                <li>• Track your progress over time</li>
                <li>• Adjust timing based on your energy levels</li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-red-50 to-rose-50 dark:from-red-900/20 dark:to-rose-900/20 p-6 rounded-lg border border-red-200 dark:border-red-700">
              <h3 className="text-lg font-semibold mb-3 text-red-900 dark:text-red-100">
                🚫 Common Mistakes
              </h3>
              <ul className="text-sm text-red-800 dark:text-red-200 space-y-2">
                <li>• Don&apos;t multitask during timer sessions</li>
                <li>• Avoid checking social media mid-session</li>
                <li>• Don&apos;t skip breaks between sessions</li>
                <li>• Don&apos;t set unrealistic expectations</li>
              </ul>
            </div>

            <div className="bg-gradient-to-br from-teal-50 to-cyan-50 dark:from-teal-900/20 dark:to-cyan-900/20 p-6 rounded-lg border border-teal-200 dark:border-teal-700">
              <h3 className="text-lg font-semibold mb-3 text-teal-900 dark:text-teal-100">
                🔄 Building Habits
              </h3>
              <ul className="text-sm text-teal-800 dark:text-teal-200 space-y-2">
                <li>• Start with shorter sessions if you&apos;re new</li>
                <li>• Use the same time daily for consistency</li>
                <li>• Create a pre-timer ritual</li>
                <li>• Track your streak to stay motivated</li>
              </ul>
            </div>
          </div>
        </section>

        {/* How to Use Section */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            How to Use the {preset.name}
          </h2>
          <div className="max-w-4xl mx-auto">
            <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
              <ol className="space-y-4 text-gray-700 dark:text-gray-300">
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-0.5">1</span>
                  <div>
                    <strong>Instant Start:</strong> The {preset.minutes}-minute timer is already set and ready to go.
                    Simply click the &quot;Start&quot; button to begin your countdown.
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-0.5">2</span>
                  <div>
                    <strong>Customize Settings:</strong> Click the settings panel to choose your preferred notification sound, 
                    background, and display options.
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-0.5">3</span>
                  <div>
                    <strong>Fullscreen Mode:</strong> Use fullscreen mode for distraction-free timing with sleep prevention 
                    to ensure your timer never stops.
                  </div>
                </li>
                <li className="flex items-start">
                  <span className="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-4 mt-0.5">4</span>
                  <div>
                    <strong>Control Your Timer:</strong> Pause, resume, or reset your timer as needed. 
                    The timer will alert you with sound when the {preset.minutes} minutes are up.
                  </div>
                </li>
              </ol>
            </div>
          </div>
        </section>

        {/* Related Timers */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            Other Timer Durations
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {relatedPresets.map((relatedPreset) => (
              <Link
                key={relatedPreset.id}
                href={`/timer/${relatedPreset.id}`}
                className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md hover:shadow-lg transition-shadow border border-gray-200 dark:border-gray-700 text-center"
              >
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                  {relatedPreset.minutes}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  {relatedPreset.minutes} Minute Timer
                </div>
              </Link>
            ))}
          </div>
          <div className="text-center">
            <Link
              href="/timer"
              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
            >
              View All Timer Options
            </Link>
          </div>
        </section>

        {/* Popular Timers */}
        <section className="space-y-8">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            Most Popular Online Timers
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {popularPresets.map((popularPreset) => (
              <Link
                key={popularPreset.id}
                href={`/timer/${popularPreset.id}`}
                className={`p-4 rounded-lg text-center transition-all ${
                  popularPreset.id === preset.id
                    ? 'bg-blue-100 dark:bg-blue-900/30 border-2 border-blue-500'
                    : 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
                }`}
              >
                <div className="text-lg font-bold text-gray-900 dark:text-white mb-1">
                  {popularPreset.minutes} min
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  {popularPreset.name}
                </div>
              </Link>
            ))}
          </div>
        </section>

        {/* FAQ Section */}
        <section className="space-y-6">
          <h2 className="text-3xl font-bold text-center text-gray-900 dark:text-white">
            Frequently Asked Questions About {minutesInWords} Minute Timers
          </h2>
          <div className="space-y-4 max-w-4xl mx-auto">
            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                What makes a {preset.minutes}-minute timer special?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                A {preset.minutes}-minute timer is perfect for {preset.description.toLowerCase()}. This duration is scientifically proven to be optimal for maintaining focus without causing mental fatigue, making it ideal for productivity techniques and various daily activities.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                How do I get the most out of my {preset.minutes} minute sessions?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                To maximize your {preset.minutes}-minute timer sessions: 1) Set a clear, specific goal before starting, 2) Eliminate all distractions, 3) Use fullscreen mode for focus, 4) Take proper breaks between sessions, and 5) Track your progress to build momentum.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                Can I use this {preset.minutes} minute timer on mobile devices?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                Yes! Our {preset.minutes}-minute timer is fully responsive and works perfectly on smartphones, tablets, and desktop computers. The interface adapts to your screen size, and the wake lock feature prevents your device from sleeping during timer sessions.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                What happens when my {preset.minutes} minute timer finishes?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                When your {preset.minutes}-minute timer completes, you&apos;ll receive both visual and audio notifications. The timer will play your selected sound and display a completion message. You can then choose to start a new {preset.minutes}-minute session or close the notification.
              </p>
            </details>

            <details className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <summary className="font-semibold text-lg cursor-pointer text-gray-900 dark:text-white">
                Is this {minutesInWords.toLowerCase()} minute timer free to use?
              </summary>
              <p className="mt-3 text-gray-600 dark:text-gray-300">
                Absolutely! Our {preset.minutes}-minute timer is completely free with no registration required. You get access to all features including custom sounds, background customization, fullscreen mode, and wake lock functionality without any cost or limitations.
              </p>
            </details>
          </div>
        </section>

        {/* Navigation Links */}
        <section className="text-center">
          <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
            More Time Management Tools
          </h2>
          <div className="flex flex-wrap justify-center gap-4">
            <Link
              href="/time"
              className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              World Time Clock
            </Link>
            <Link
              href="/alarm"
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Alarm Clock
            </Link>
            <Link
              href="/stopwatch"
              className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Stopwatch
            </Link>
          </div>
        </section>

        <Footer />
      </div>
    </div>
  );
}
