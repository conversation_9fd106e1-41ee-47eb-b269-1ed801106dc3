"use client";

import { useState, useEffect } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";

import { Settings, X, Monitor, Type, Palette, Move } from "lucide-react";

interface ClockSettingsProps {
  showSeconds: boolean;
  setShowSeconds: (value: boolean) => void;
  showWeekday: boolean;
  setShowWeekday: (value: boolean) => void;
  showDate: boolean;
  setShowDate: (value: boolean) => void;
  showWeekNumber: boolean;
  setShowWeekNumber: (value: boolean) => void;
  use12Hours?: boolean;
  setUse12Hours?: (value: boolean) => void;
  textColor: string;
  setTextColor: (value: string) => void;
  fontSize: string;
  setFontSize: (value: string) => void;
  fontFamily: string;
  setFontFamily: (value: string) => void;
  position?: { x: number; y: number };
  setPosition?: (value: { x: number; y: number }) => void;
  backgroundColor?: string;
  setBackgroundColor?: (value: string) => void;
  backgroundImage?: string;
  setBackgroundImage?: (value: string) => void;
  isFullScreen?: boolean;
}

export function ClockSettings({
  showSeconds,
  setShowSeconds,
  showWeekday,
  setShowWeekday,
  showDate,
  setShowDate,
  showWeekNumber,
  setShowWeekNumber,
  use12Hours = false,
  setUse12Hours = () => {},
  textColor,
  setTextColor,
  fontSize,
  setFontSize,
  fontFamily,
  setFontFamily,
  position = { x: 0, y: 0 },
  setPosition = () => {},
  backgroundColor = "",
  setBackgroundColor = () => {},
  backgroundImage = "",
  setBackgroundImage = () => {},
  isFullScreen = false,
}: ClockSettingsProps) {
  const [fontSizeValue, setFontSizeValue] = useState(() => parseInt(fontSize));
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [activeTab, setActiveTab] = useState(0);

  // Dynamic font size ranges based on screen size
  const getFontSizeRange = () => {
    if (isMobile) {
      return { min: 2, max: 8, default: 4, step: 0.5 }; // Mobile: 2rem - 8rem
    } else if (isTablet) {
      return { min: 3, max: 15, default: 6, step: 1 }; // Tablet: 3rem - 15rem
    } else {
      return { min: 4, max: 24, default: 8, step: 1 }; // Desktop: 4rem - 24rem
    }
  };

  const fontRange = getFontSizeRange();

  // Set mounted state and detect device type
  useEffect(() => {
    setIsMounted(true);

    const updateDeviceState = () => {
      const width = window.innerWidth;
      const mobile = width < 768;
      const tablet = width >= 768 && width < 1024;

      setIsMobile(mobile);
      setIsTablet(tablet);

      // Auto collapse on mobile
      if (mobile) {
        setIsCollapsed(true);
      }

      console.log('Device detected:', { width, mobile, tablet, desktop: !mobile && !tablet });
    };

    updateDeviceState();
    window.addEventListener('resize', updateDeviceState);

    return () => {
      window.removeEventListener('resize', updateDeviceState);
    };
  }, []);

  // Update fontSizeValue when fontSize prop changes (but prevent infinite loops)
  useEffect(() => {
    const newValue = parseInt(fontSize);
    if (newValue !== fontSizeValue) {
      console.log('Updating fontSizeValue from fontSize prop:', fontSize, '->', newValue);
      setFontSizeValue(newValue);
    }
  }, [fontSize]);

  // Update fontSize when fontSizeValue changes (with debounce to prevent infinite loops)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      const newFontSize = `${fontSizeValue}rem`;
      if (newFontSize !== fontSize) {
        setFontSize(newFontSize);
      }
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [fontSizeValue, fontSize, setFontSize]);

  // Handle fullscreen changes (only after mount)
  useEffect(() => {
    if (!isMounted) return;

    if (isFullScreen) {
      setIsCollapsed(true);
    }
  }, [isMounted, isFullScreen]);

  const fontOptions = [
    { value: "monospace", label: "Monospace" },
    { value: "sans-serif", label: "Sans Serif" },
    { value: "serif", label: "Serif" },
    { value: "Arial", label: "Arial" },
    { value: "Verdana", label: "Verdana" },
    { value: "Helvetica", label: "Helvetica" },
    { value: "Times New Roman", label: "Times New Roman" },
    { value: "Courier New", label: "Courier New" },
  ];

  const mobileTabsConfig = [
    {
      id: 0,
      label: "Display",
      icon: Monitor,
      color: "text-blue-600"
    },
    {
      id: 1,
      label: "Typography",
      icon: Type,
      color: "text-purple-600"
    },
    {
      id: 2,
      label: "Background",
      icon: Palette,
      color: "text-green-600"
    },
    {
      id: 3,
      label: "Position",
      icon: Move,
      color: "text-orange-600"
    }
  ];

  const handleMove = (direction: 'up' | 'down' | 'left' | 'right') => {
    const step = 20; // pixels to move per click
    const newPosition = { ...position };

    switch (direction) {
      case 'up':
        newPosition.y -= step;
        break;
      case 'down':
        newPosition.y += step;
        break;
      case 'left':
        newPosition.x -= step;
        break;
      case 'right':
        newPosition.x += step;
        break;
    }

    setPosition(newPosition);
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  // Mobile tab content components
  const renderMobileTabContent = () => {
    switch (activeTab) {
      case 0: // Display
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-3">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="showSeconds"
                  checked={showSeconds}
                  onChange={(e) => setShowSeconds(e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                />
                <label htmlFor="showSeconds" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Show Seconds
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="use12Hours"
                  checked={use12Hours}
                  onChange={(e) => setUse12Hours(e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                />
                <label htmlFor="use12Hours" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  12 Hours
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="showWeekday"
                  checked={showWeekday}
                  onChange={(e) => setShowWeekday(e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                />
                <label htmlFor="showWeekday" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Show Weekday
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="showDate"
                  checked={showDate}
                  onChange={(e) => setShowDate(e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                />
                <label htmlFor="showDate" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Show Date
                </label>
              </div>
              <div className="flex items-center space-x-2 col-span-2">
                <input
                  type="checkbox"
                  id="showWeekNumber"
                  checked={showWeekNumber}
                  onChange={(e) => setShowWeekNumber(e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                />
                <label htmlFor="showWeekNumber" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Show Week Number
                </label>
              </div>
            </div>
          </div>
        );

      case 1: // Typography
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Text Color
              </label>
              <div className="relative">
                <input
                  type="color"
                  value={textColor}
                  onChange={(e) => setTextColor(e.target.value)}
                  className="w-full h-11 rounded-xl border-2 border-gray-200 dark:border-gray-700 cursor-pointer"
                />
                <div className="absolute inset-0 rounded-xl border-2 border-gray-200 dark:border-gray-700 pointer-events-none"></div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Font Size
              </label>
              <div className="flex items-center gap-3">
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Decrease button clicked, current value:', fontSizeValue, 'range:', fontRange);
                    const newValue = Math.max(fontRange.min, fontSizeValue - fontRange.step);
                    console.log('Setting new value:', newValue);
                    setFontSizeValue(newValue);
                  }}
                  className="w-12 h-12 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 rounded-xl flex items-center justify-center font-bold text-xl transition-all duration-200 hover:scale-105 active:scale-95"
                  disabled={fontSizeValue <= fontRange.min}
                >
                  −
                </button>

                <div className="flex-1 bg-gray-50 dark:bg-gray-800 rounded-xl p-4">
                  <div className="relative h-8 bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden">
                    <div
                      className="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg transition-all duration-300 ease-out"
                      style={{ width: `${((fontSizeValue - fontRange.min) / (fontRange.max - fontRange.min)) * 100}%` }}
                    />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300 mix-blend-difference">
                        {fontSizeValue}rem
                      </span>
                    </div>
                  </div>
                  <div className="flex justify-between mt-2 text-xs text-gray-500 dark:text-gray-400">
                    <span>{fontRange.min}rem</span>
                    <span>{fontRange.max}rem</span>
                  </div>
                </div>

                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Increase button clicked, current value:', fontSizeValue, 'range:', fontRange);
                    const newValue = Math.min(fontRange.max, fontSizeValue + fontRange.step);
                    console.log('Setting new value:', newValue);
                    setFontSizeValue(newValue);
                  }}
                  className="w-12 h-12 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 rounded-xl flex items-center justify-center font-bold text-xl transition-all duration-200 hover:scale-105 active:scale-95"
                  disabled={fontSizeValue >= fontRange.max}
                >
                  +
                </button>
              </div>


            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Font Family
              </label>
              <Select value={fontFamily} onValueChange={setFontFamily}>
                <SelectTrigger className="h-11 bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700 rounded-xl">
                  <SelectValue placeholder="Choose font" />
                </SelectTrigger>
                <SelectContent>
                  {fontOptions.map((font) => (
                    <SelectItem key={font.value} value={font.value}>
                      {font.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        );

      case 2: // Background
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Background Color
              </label>
              <div className="flex gap-3">
                <div className="relative flex-1">
                  <input
                    type="color"
                    value={backgroundColor || "#ffffff"}
                    onChange={(e) => {
                      setBackgroundColor(e.target.value);
                      setBackgroundImage('');
                    }}
                    className="w-full h-11 rounded-xl border-2 border-gray-200 dark:border-gray-700 cursor-pointer"
                  />
                  <div className="absolute inset-0 rounded-xl border-2 border-gray-200 dark:border-gray-700 pointer-events-none"></div>
                </div>
                <button
                  onClick={() => setBackgroundColor('')}
                  className="px-3 py-2 bg-red-100 hover:bg-red-200 text-red-700 dark:bg-red-900/30 dark:hover:bg-red-900/50 dark:text-red-400 rounded-xl text-sm font-medium transition-colors duration-200"
                >
                  Clear
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Image URL
              </label>
              <input
                type="text"
                value={backgroundImage}
                onChange={(e) => {
                  setBackgroundImage(e.target.value);
                  setBackgroundColor('');
                }}
                placeholder="Enter image URL"
                className="w-full h-11 px-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Upload Image
              </label>
              <input
                type="file"
                accept="image/*"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    const reader = new FileReader();
                    reader.onload = (event) => {
                      const base64String = event.target?.result as string;
                      setBackgroundImage(base64String);
                      setBackgroundColor('');
                    };
                    reader.readAsDataURL(file);
                  }
                }}
                className="w-full h-11 px-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl text-sm file:mr-3 file:py-2 file:px-3 file:rounded-lg file:border-0 file:text-sm file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 cursor-pointer"
              />
            </div>

            <button
              onClick={() => {
                setBackgroundImage('');
                setBackgroundColor('');
              }}
              className="w-full py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300 rounded-xl text-sm font-medium transition-colors duration-200"
            >
              Clear All Backgrounds
            </button>
          </div>
        );

      case 3: // Position
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-2">
              <div></div>
              <Button
                variant="outline"
                onClick={() => handleMove('up')}
                className="h-11 border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl"
              >
                ↑
              </Button>
              <div></div>

              <Button
                variant="outline"
                onClick={() => handleMove('left')}
                className="h-11 border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl"
              >
                ←
              </Button>
              <Button
                variant="outline"
                onClick={() => setPosition({ x: 0, y: 0 })}
                className="h-11 text-xs border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl font-medium"
              >
                Center
              </Button>
              <Button
                variant="outline"
                onClick={() => handleMove('right')}
                className="h-11 border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl"
              >
                →
              </Button>

              <div></div>
              <Button
                variant="outline"
                onClick={() => handleMove('down')}
                className="h-11 border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl"
              >
                ↓
              </Button>
              <div></div>
            </div>

            <div className="text-center">
              <div className="inline-flex items-center gap-2 px-3 py-2 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg text-sm">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                Position: ({position?.x || 0}, {position?.y || 0})
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      {/* Floating Settings Button */}
      {isCollapsed && (
        <div
          className="group"
          style={{
            position: 'fixed',
            bottom: '24px',
            right: '24px',
            zIndex: 50,
          }}
        >
          <button
            onClick={toggleCollapse}
            className="w-14 h-14 bg-gradient-to-br from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group-hover:scale-110"
          >
            <Settings size={22} className="transition-transform duration-300 group-hover:rotate-90" />
          </button>
        </div>
      )}

      {/* Settings Panel */}
      {!isCollapsed && (
        <div
          className={`${
            isMobile 
              ? 'bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 shadow-xl w-full h-[50vh] rounded-t-3xl'
              : 'bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-700 shadow-2xl w-96 h-[90vh] rounded-3xl'
          } flex flex-col`}
          style={{
            position: 'fixed',
            zIndex: 50,
            ...(isMobile ? {
              bottom: '0px',
              left: '0px',
              right: '0px',
            } : {
              bottom: '24px',
              right: '24px',
            }),
          }}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200/50 dark:border-gray-700/50">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Settings size={16} className="text-white" />
              </div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Clock Settings
              </h2>
            </div>
            <button
              onClick={toggleCollapse}
              className="w-8 h-8 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center justify-center transition-colors duration-200"
            >
              <X size={18} className="text-gray-500 dark:text-gray-400" />
            </button>
          </div>

          {/* Mobile Tabs */}
          {isMobile && (
            <div className="flex border-b border-gray-200/50 dark:border-gray-700/50">
              {mobileTabsConfig.map((tab) => {
                const IconComponent = tab.icon;
                const isActive = activeTab === tab.id;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex-1 flex flex-col items-center gap-1 py-3 px-2 transition-all duration-200 ${
                      isActive
                        ? 'bg-blue-50 dark:bg-blue-900/20 border-b-2 border-blue-500'
                        : 'hover:bg-gray-50 dark:hover:bg-gray-800/50'
                    }`}
                  >
                    <IconComponent 
                      size={16} 
                      className={isActive ? 'text-blue-600' : 'text-gray-500 dark:text-gray-400'} 
                    />
                    <span className={`text-xs font-medium ${
                      isActive ? 'text-blue-600' : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      {tab.label}
                    </span>
                  </button>
                );
              })}
            </div>
          )}

          {/* Content */}
          {isMobile ? (
            // Mobile tabbed content
            <div className="flex-1 overflow-y-auto p-6">
              {renderMobileTabContent()}
            </div>
          ) : (
            // Desktop content
            <div className="flex-1 overflow-y-auto p-6 space-y-8">
              
              {/* Display Options */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Monitor size={18} className="text-blue-600" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">Display</h3>
                </div>
                
                <div className="bg-gray-50/50 dark:bg-gray-800/50 rounded-2xl p-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="showSeconds"
                        checked={showSeconds}
                        onChange={(e) => setShowSeconds(e.target.checked)}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                      />
                      <label htmlFor="showSeconds" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Show Seconds
                      </label>
                    </div>
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="use12Hours"
                        checked={use12Hours}
                        onChange={(e) => setUse12Hours(e.target.checked)}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                      />
                      <label htmlFor="use12Hours" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        12 Hours
                      </label>
                    </div>
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="showWeekday"
                        checked={showWeekday}
                        onChange={(e) => setShowWeekday(e.target.checked)}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                      />
                      <label htmlFor="showWeekday" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Show Weekday
                      </label>
                    </div>
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="showDate"
                        checked={showDate}
                        onChange={(e) => setShowDate(e.target.checked)}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                      />
                      <label htmlFor="showDate" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Show Date
                      </label>
                    </div>
                    <div className="flex items-center space-x-3 col-span-2">
                      <input
                        type="checkbox"
                        id="showWeekNumber"
                        checked={showWeekNumber}
                        onChange={(e) => setShowWeekNumber(e.target.checked)}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                      />
                      <label htmlFor="showWeekNumber" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Show Week Number
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              {/* Typography */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Type size={18} className="text-purple-600" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">Typography</h3>
                </div>
                
                <div className="bg-gray-50/50 dark:bg-gray-800/50 rounded-2xl p-4 space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Text Color
                    </label>
                    <div className="relative">
                      <input
                        type="color"
                        value={textColor}
                        onChange={(e) => setTextColor(e.target.value)}
                        className="w-full h-12 rounded-xl border-2 border-gray-200 dark:border-gray-700 cursor-pointer"
                      />
                      <div className="absolute inset-0 rounded-xl border-2 border-gray-200 dark:border-gray-700 pointer-events-none"></div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      Font Size
                    </label>
                    <div className="flex items-center gap-4">
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          console.log('Desktop decrease button clicked, current value:', fontSizeValue, 'range:', fontRange);
                          const newValue = Math.max(fontRange.min, fontSizeValue - fontRange.step);
                          console.log('Desktop setting new value:', newValue);
                          setFontSizeValue(newValue);
                        }}
                        className="w-14 h-14 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 rounded-xl flex items-center justify-center font-bold text-2xl transition-all duration-200 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg"
                        disabled={fontSizeValue <= fontRange.min}
                      >
                        −
                      </button>

                      <div className="flex-1 bg-gray-50 dark:bg-gray-800 rounded-xl p-5">
                        <div className="relative h-10 bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden">
                          <div
                            className="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg transition-all duration-300 ease-out"
                            style={{ width: `${((fontSizeValue - fontRange.min) / (fontRange.max - fontRange.min)) * 100}%` }}
                          />
                          <div className="absolute inset-0 flex items-center justify-center">
                            <span className="text-base font-semibold text-gray-700 dark:text-gray-300 mix-blend-difference">
                              {fontSizeValue}rem
                            </span>
                          </div>
                        </div>
                        <div className="flex justify-between mt-3 text-sm text-gray-500 dark:text-gray-400">
                          <span>{fontRange.min}rem</span>
                          <span>{fontRange.max}rem</span>
                        </div>
                      </div>

                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          console.log('Desktop increase button clicked, current value:', fontSizeValue, 'range:', fontRange);
                          const newValue = Math.min(fontRange.max, fontSizeValue + fontRange.step);
                          console.log('Desktop setting new value:', newValue);
                          setFontSizeValue(newValue);
                        }}
                        className="w-14 h-14 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-700 dark:text-blue-300 rounded-xl flex items-center justify-center font-bold text-2xl transition-all duration-200 hover:scale-105 active:scale-95 shadow-md hover:shadow-lg"
                        disabled={fontSizeValue >= fontRange.max}
                      >
                        +
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Font Family
                    </label>
                    <Select value={fontFamily} onValueChange={setFontFamily}>
                      <SelectTrigger className="h-12 bg-white dark:bg-gray-900 border-gray-200 dark:border-gray-700 rounded-xl">
                        <SelectValue placeholder="Choose font" />
                      </SelectTrigger>
                      <SelectContent>
                        {fontOptions.map((font) => (
                          <SelectItem key={font.value} value={font.value}>
                            {font.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Background */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Palette size={18} className="text-green-600" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">Background</h3>
                </div>
                
                <div className="bg-gray-50/50 dark:bg-gray-800/50 rounded-2xl p-4 space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Background Color
                    </label>
                    <div className="flex gap-3">
                      <div className="relative flex-1">
                        <input
                          type="color"
                          value={backgroundColor || "#ffffff"}
                          onChange={(e) => {
                            setBackgroundColor(e.target.value);
                            setBackgroundImage('');
                          }}
                          className="w-full h-12 rounded-xl border-2 border-gray-200 dark:border-gray-700 cursor-pointer"
                        />
                        <div className="absolute inset-0 rounded-xl border-2 border-gray-200 dark:border-gray-700 pointer-events-none"></div>
                      </div>
                      <button
                        onClick={() => setBackgroundColor('')}
                        className="px-4 py-2 bg-red-100 hover:bg-red-200 text-red-700 dark:bg-red-900/30 dark:hover:bg-red-900/50 dark:text-red-400 rounded-xl text-sm font-medium transition-colors duration-200"
                      >
                        Clear
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Image URL
                    </label>
                    <input
                      type="text"
                      value={backgroundImage}
                      onChange={(e) => {
                        setBackgroundImage(e.target.value);
                        setBackgroundColor('');
                      }}
                      placeholder="Enter image URL"
                      className="w-full h-12 px-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Upload Image
                    </label>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file) {
                          const reader = new FileReader();
                          reader.onload = (event) => {
                            const base64String = event.target?.result as string;
                            setBackgroundImage(base64String);
                            setBackgroundColor('');
                          };
                          reader.readAsDataURL(file);
                        }
                      }}
                      className="w-full h-12 px-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl text-sm file:mr-3 file:py-2 file:px-3 file:rounded-lg file:border-0 file:text-sm file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 cursor-pointer"
                    />
                  </div>

                  <button
                    onClick={() => {
                      setBackgroundImage('');
                      setBackgroundColor('');
                    }}
                    className="w-full py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300 rounded-xl text-sm font-medium transition-colors duration-200"
                  >
                    Clear All Backgrounds
                  </button>
                </div>
              </div>

              {/* Position Controls */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Move size={18} className="text-orange-600" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">Position</h3>
                </div>
                
                <div className="bg-gray-50/50 dark:bg-gray-800/50 rounded-2xl p-4 space-y-4">
                  <div className="grid grid-cols-3 gap-2">
                    <div></div>
                    <Button
                      variant="outline"
                      onClick={() => handleMove('up')}
                      className="h-12 border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl"
                    >
                      ↑
                    </Button>
                    <div></div>

                    <Button
                      variant="outline"
                      onClick={() => handleMove('left')}
                      className="h-12 border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl"
                    >
                      ←
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setPosition({ x: 0, y: 0 })}
                      className="h-12 text-xs border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl font-medium"
                    >
                      Center
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => handleMove('right')}
                      className="h-12 border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl"
                    >
                      →
                    </Button>

                    <div></div>
                    <Button
                      variant="outline"
                      onClick={() => handleMove('down')}
                      className="h-12 border-gray-300 dark:border-gray-600 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20 rounded-xl"
                    >
                      ↓
                    </Button>
                    <div></div>
                  </div>

                  <div className="text-center">
                    <div className="inline-flex items-center gap-2 px-3 py-2 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 rounded-lg text-sm">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      Position: ({position?.x || 0}, {position?.y || 0})
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Footer */}
          <div className={`border-t border-gray-200/50 dark:border-gray-700/50 ${isMobile ? 'p-4' : 'p-6'}`}>
            <button
              onClick={toggleCollapse}
              className="w-full h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              Done
            </button>
          </div>
        </div>
      )}
    </>
  );
}
